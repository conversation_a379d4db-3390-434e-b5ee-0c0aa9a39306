"""
HTML Comparison Service using LLM for intelligent HTML comparison and analysis.
"""

from langchain_openai import ChatOpenAI
from langchain.schema import SystemMessage, HumanMessage
from typing import List, Dict, Any, Optional
import uuid
import time
from datetime import datetime
import json
import re
from bs4 import BeautifulSoup

import httpx
import logging
from app.core.config import settings
from app.models.schemas import HTMLComparisonRequest, HTMLComparisonResponse, HTMLDifference, HTMLComparisonByIdRequest
from fastapi import HTTPException
from app.services.rag_service import RAGService

class HTMLComparisonService:
    """Service for comparing HTML content using LLM analysis."""
    
    def __init__(self):
        """Initialize the HTML comparison service."""
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            openai_api_key=settings.OPENAI_API_KEY,
            temperature=0,
            seed=42,  # Deterministic results
            max_retries=3,
            max_tokens=settings.MAX_TOKENS
        ) if settings.OPENAI_API_KEY else None
        self.rag_service = RAGService()
        self.html_examples_collection = "html_comparison_examples"
        self.logger = logging.getLogger(__name__)

    async def initialize_html_comparison_knowledge_base(self, examples: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Initialize RAG knowledge base with correct HTML comparison examples."""
        return await self.rag_service.initialize_knowledge_base(examples)

    async def _fetch_html_from_api(self, report_id: str, token: str) -> Dict[str, str]:
        """Fetch HTML content from the external API and return only the relevant HTML fields."""
        if not settings.HTML_COMPARISON_API_URL:
            self.logger.error("HTML_COMPARISON_API_URL is not configured.")
            raise HTTPException(status_code=500, detail="HTML_COMPARISON_API_URL is not configured.")

        api_url = settings.HTML_COMPARISON_API_URL.format(reportId=report_id)
        headers = {
            "Authorization": f"Bearer {token}",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"
        }

        self.logger.info(f"Fetching HTML from API: {api_url}")
        self.logger.info(f"Using token (first 10 chars): {token[:10]}... Report ID: {report_id}")
        self.logger.info(f"Request headers: {headers}")

        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(api_url, headers=headers)
                self.logger.info(f"External API response status: {response.status_code}")
                self.logger.debug(f"External API response content: {response.text}")
                response.raise_for_status()
                data = response.json()
                # Ensure the expected keys are present
                if not ("requestedOrderHtml" in data and "finalizedOrderHTML" in data):
                    self.logger.error(f"API response missing expected keys: {data}")
                    raise HTTPException(status_code=502, detail="External API did not return expected HTML fields.")
                return {
                    "requestedOrderHtml": data["requestedOrderHtml"],
                    "finalizedOrderHTML": data["finalizedOrderHTML"]
                }
            except httpx.HTTPStatusError as e:
                self.logger.error(f"External API error: {e.response.status_code}")
                self.logger.error(f"Response body: {e.response.text}")
                raise HTTPException(
                    status_code=e.response.status_code, 
                    detail=f"External API error: {e.response.status_code}. Response: {e.response.text}"
                )
            except httpx.RequestError as e:
                self.logger.error(f"External API request failed: {e!r}")
                raise HTTPException(status_code=500, detail=f"External API request failed: {e!r}")
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to decode JSON response from external API: {e}")
                self.logger.error(f"Response text: {response.text}")
                raise HTTPException(status_code=500, detail="Failed to decode JSON response from external API.")

    async def compare_html_by_id(self, request: HTMLComparisonByIdRequest) -> HTMLComparisonResponse:
        """
        Compare two HTML documents fetched by report ID.
        """
        html_data = await self._fetch_html_from_api(request.report_id, request.bearer_token)
        
        comparison_request = HTMLComparisonRequest(
            requested_order_html=html_data.get("requestedOrderHtml", ""),
            finalized_order_html=html_data.get("finalizedOrderHTML", "")
        )
        
        return await self.compare_html(comparison_request)
        
    async def compare_html(self, request: HTMLComparisonRequest) -> HTMLComparisonResponse:
        """
        Compare two HTML documents and identify differences using LLM analysis.
        
        Args:
            request: HTMLComparisonRequest containing the two HTML documents
            
        Returns:
            HTMLComparisonResponse with detailed comparison results
        """
        start_time = time.time()
        comparison_id = str(uuid.uuid4())
        
        try:
            # Validate and preprocess HTML content for better comparison
            cleaned_requested = self._clean_and_validate_html(request.requested_order_html)
            cleaned_finalized = self._clean_and_validate_html(request.finalized_order_html)

            # Log HTML sizes for debugging
            self.logger.info(f"Cleaned HTML sizes - Requested: {len(cleaned_requested)} chars, Finalized: {len(cleaned_finalized)} chars")
            
            # Generate LLM analysis
            if self.llm:
                analysis_result = await self._generate_llm_comparison(
                    cleaned_requested, 
                    cleaned_finalized
                )
            else:
                # Fallback to basic comparison if LLM is not available
                analysis_result = self._generate_fallback_comparison(
                    cleaned_requested, 
                    cleaned_finalized
                )
            
            # Parse the analysis result
            parsed_result = self._parse_comparison_result(analysis_result)

            # Calculate actual total changes from parsed elements
            actual_total_changes = (
                len(parsed_result.get("added_elements", [])) +
                len(parsed_result.get("removed_elements", [])) +
                len(parsed_result.get("modified_elements", []))
            )

            # Calculate processing time
            processing_time = time.time() - start_time

            # Build response
            response = HTMLComparisonResponse(
                comparison_id=comparison_id,
                timestamp=datetime.now(),
                summary=parsed_result.get("summary", "HTML comparison completed"),
                total_changes=actual_total_changes,  # Use calculated count
                added_elements=parsed_result.get("added_elements", []),
                removed_elements=parsed_result.get("removed_elements", []),
                modified_elements=parsed_result.get("modified_elements", []),
                processing_time=processing_time,
                confidence_score=parsed_result.get("confidence_score", 0.8)
            )
            
            return response
            
        except Exception as e:
            # Log the error for debugging
            self.logger.error(f"Error during HTML comparison: {str(e)}", exc_info=True)

            # Return error response
            return HTMLComparisonResponse(
                comparison_id=comparison_id,
                timestamp=datetime.now(),
                summary=f"Error during HTML comparison: {str(e)}",
                total_changes=0,
                added_elements=[],
                removed_elements=[],
                modified_elements=[],
                processing_time=time.time() - start_time,
                confidence_score=0.0
            )
    
    def _clean_and_validate_html(self, html_content: str) -> str:
        """
        Clean, validate and normalize HTML content for better comparison.

        Args:
            html_content: Raw HTML content

        Returns:
            Cleaned and validated HTML content
        """
        if not html_content or not isinstance(html_content, str):
            self.logger.warning("Empty or invalid HTML content provided")
            return ""

        # Truncate extremely large HTML to prevent processing issues
        max_html_size = 100000  # 100KB limit
        if len(html_content) > max_html_size:
            self.logger.warning(f"HTML content truncated from {len(html_content)} to {max_html_size} characters")
            html_content = html_content[:max_html_size] + "..."

        return self._clean_html(html_content)

    def _clean_html(self, html_content: str) -> str:
        """
        Clean and normalize HTML content for better comparison.
        Removes style-related attributes to focus only on content and structure.
        
        Args:
            html_content: Raw HTML content
            
        Returns:
            Cleaned HTML content with style attributes removed
        """
        try:
            # Parse HTML with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove comments
            for comment in soup.find_all(string=lambda text: isinstance(text, str) and text.strip().startswith('<!--')):
                comment.extract()
            
            # Remove style-related attributes to focus on content only
            style_attributes = ['style', 'class', 'id', 'width', 'height', 'bgcolor', 'color', 'font', 'align', 'valign']
            for element in soup.find_all():
                for attr in style_attributes:
                    if element.has_attr(attr):
                        del element[attr]
            
            # Remove <style> tags entirely
            for style_tag in soup.find_all('style'):
                style_tag.decompose()
            
            # Remove <link> tags that reference CSS
            for link_tag in soup.find_all('link', rel='stylesheet'):
                link_tag.decompose()
            
            # Normalize whitespace
            for element in soup.find_all(text=True):
                if element.strip():
                    element.replace_with(re.sub(r'\s+', ' ', element.strip()))
            
            # Return prettified HTML
            return soup.prettify()
            
        except Exception:
            # If parsing fails, return original content
            return html_content
    
    async def _generate_llm_comparison(self, html1: str, html2: str) -> str:
        # Retrieve relevant examples with deterministic ordering
        search_query = self._create_html_search_query(html1, html2)
        rag_data = await self.rag_service.retrieve_relevant_examples(
            search_query,
            f"{html1[:500]}...{html2[:500]}",
            n_results=3
        )
        
        # Ensure deterministic RAG data ordering
        rag_data = self._ensure_deterministic_rag_data(rag_data)

        # Build RAG-enhanced prompt
        system_prompt = self._build_rag_enhanced_html_prompt(rag_data)

        user_prompt = f"""Compare these HTML documents using the knowledge base examples:

        REQUESTED ORDER HTML: {html1}
        FINALIZED ORDER HTML: {html2}

        Follow the patterns shown in the examples for accurate change detection and counting."""

        # First attempt
        response = await self.llm.ainvoke([
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ])

        # Validate JSON format and retry if needed
        try:
            # Test if response is valid JSON
            cleaned_response = response.content.strip()
            if cleaned_response.startswith('```json
'):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.startswith('
```'):
                cleaned_response = cleaned_response[3:]
            if cleaned_response.endswith('```python
'):
                cleaned_response = cleaned_response[:-3]
            cleaned_response = cleaned_response.strip()

            json.loads(cleaned_response)  # Test parsing
            return response.content

        except json.JSONDecodeError:
            self.logger.warning("First LLM response was not valid JSON, retrying with explicit instructions")

            # Retry with more explicit JSON formatting instructions
            retry_prompt = f"""The previous response was not valid JSON. Please provide ONLY a valid JSON response with no additional text.

Compare these HTML documents:

REQUESTED ORDER HTML: {html1}
FINALIZED ORDER HTML: {html2}

Return ONLY this JSON structure (no markdown, no explanations):
{{
    "summary": "description of changes",
    "total_changes": 0,
    "confidence_score": 0.8,
    "added_elements": [],
    "removed_elements": [],
    "modified_elements": []
}}"""

            retry_response = await self.llm.ainvoke([
                SystemMessage(content="You are a JSON formatter. Return only valid JSON with no additional text."),
                HumanMessage(content=retry_prompt)
            ])

            return retry_response.content
    
    def _generate_fallback_comparison(self, html1: str, html2: str) -> str:
        """
        Generate basic comparison when LLM is not available.
        Focuses on content changes only, ignoring style differences.
        
        Args:
            html1: First HTML document
            html2: Second HTML document
            
        Returns:
            Basic comparison result as JSON string
        """
        try:
            # Parse both HTML documents with BeautifulSoup for content extraction
            soup1 = BeautifulSoup(html1, 'html.parser')
            soup2 = BeautifulSoup(html2, 'html.parser')
            
            # Extract text content only (ignoring tags and styling)
            text1 = soup1.get_text(separator='\n', strip=True)
            text2 = soup2.get_text(separator='\n', strip=True)
            
            # Split into lines for comparison
            lines1 = [line.strip() for line in text1.split('\n') if line.strip()]
            lines2 = [line.strip() for line in text2.split('\n') if line.strip()]
            
            # Find content differences
            added_content = []
            removed_content = []
            
            # Find added content
            for i, line in enumerate(lines2):
                if line not in lines1 and len(line) > 3:  # Ignore very short lines
                    added_content.append(f"Line {i+1}: {line}")
            
            # Find removed content
            for i, line in enumerate(lines1):
                if line not in lines2 and len(line) > 3:  # Ignore very short lines
                    removed_content.append(f"Line {i+1}: {line}")
            
            # Also check for structural differences (new elements)
            elements1 = set([tag.name for tag in soup1.find_all() if tag.name])
            elements2 = set([tag.name for tag in soup2.find_all() if tag.name])
            
            new_elements = elements2 - elements1
            removed_elements = elements1 - elements2
            
            # Build result focusing on content changes
            added_elements = []
            removed_elements_list = []
            
            # Add content changes
            for content in added_content[:10]:  # Limit to first 10
                added_elements.append({
                    "type": "added",
                    "element": "text_content",
                    "location": "content",
                    "description": f"Added content: {content[:100]}..."
                })
            
            for content in removed_content[:10]:  # Limit to first 10
                removed_elements_list.append({
                    "type": "removed",
                    "element": "text_content",
                    "location": "content",
                    "description": f"Removed content: {content[:100]}..."
                })
            
            # Add structural changes
            for element in new_elements:
                added_elements.append({
                    "type": "added",
                    "element": element,
                    "location": "structure",
                    "description": f"New HTML element type: {element}"
                })
            
            for element in removed_elements:
                removed_elements_list.append({
                    "type": "removed",
                    "element": element,
                    "location": "structure",
                    "description": f"Removed HTML element type: {element}"
                })
            
            total_changes = len(added_elements) + len(removed_elements_list)
            
            result = {
                "summary": f"Content-focused comparison found {len(added_content)} content additions, {len(removed_content)} content removals, and {len(new_elements) + len(removed_elements)} structural changes",
                "total_changes": total_changes,
                "confidence_score": 0.6,  # Slightly higher confidence for content-focused approach
                "added_elements": added_elements,
                "removed_elements": removed_elements_list,
                "modified_elements": []  # Fallback doesn't detect modifications
            }
            
            return json.dumps(result)
            
        except Exception as e:
            # If parsing fails, fall back to simple line comparison
            lines1 = html1.split('\n')
            lines2 = html2.split('\n')
            
            added_lines = [line for line in lines2 if line not in lines1 and line.strip()]
            removed_lines = [line for line in lines1 if line not in lines2 and line.strip()]
            
            result = {
                "summary": f"Basic fallback comparison found {len(added_lines)} additions and {len(removed_lines)} removals",
                "total_changes": len(added_lines) + len(removed_lines),
                "confidence_score": 0.3,
                "added_elements": [
                    {
                        "type": "added",
                        "element": "line",
                        "location": "unknown",
                        "description": f"Added: {line[:100]}..."
                    } for line in added_lines[:5]
                ],
                "removed_elements": [
                    {
                        "type": "removed",
                        "element": "line",
                        "location": "unknown",
                        "description": f"Removed: {line[:100]}..."
                    } for line in removed_lines[:5]
                ],
                "modified_elements": []
            }
            
            return json.dumps(result)
    
    def _parse_comparison_result(self, analysis_result: str) -> Dict[str, Any]:
        """
        Parse the LLM analysis result into structured data.

        Args:
            analysis_result: Raw LLM response

        Returns:
            Parsed comparison data
        """
        try:
            # Log the raw response for debugging
            self.logger.debug(f"Raw LLM response: {analysis_result}")

            # Clean the response - remove markdown code blocks if present
            cleaned_result = analysis_result.strip()
            if cleaned_result.startswith('
```json'):
                cleaned_result = cleaned_result[7:]  # Remove ```json
if cleaned_result.startswith('
```'):
                cleaned_result = cleaned_result[3:]   # Remove ```
if cleaned_result.endswith('
```'):
                cleaned_result = cleaned_result[:-3]  # Remove trailing ```
            cleaned_result = cleaned_result.strip()

            # Try to parse as JSON
            parsed = json.loads(cleaned_result)

            # Validate required fields
            if not isinstance(parsed, dict):
                raise ValueError("Response is not a JSON object")

            # Convert dictionaries to HTMLDifference objects with error handling
            added_elements = []
            for item in parsed.get("added_elements", []):
                try:
                    added_elements.append(HTMLDifference(**item))
                except Exception as e:
                    self.logger.warning(f"Failed to parse added element: {item}, error: {e}")

            removed_elements = []
            for item in parsed.get("removed_elements", []):
                try:
                    removed_elements.append(HTMLDifference(**item))
                except Exception as e:
                    self.logger.warning(f"Failed to parse removed element: {item}, error: {e}")

            modified_elements = []
            for item in parsed.get("modified_elements", []):
                try:
                    modified_elements.append(HTMLDifference(**item))
                except Exception as e:
                    self.logger.warning(f"Failed to parse modified element: {item}, error: {e}")

            return {
                "summary": parsed.get("summary", "Comparison completed"),
                "total_changes": parsed.get("total_changes", 0),
                "confidence_score": parsed.get("confidence_score", 0.8),
                "added_elements": added_elements,
                "removed_elements": removed_elements,
                "modified_elements": modified_elements
            }

        except (json.JSONDecodeError, ValueError) as e:
            # Log the parsing error for debugging
            self.logger.error(f"Failed to parse LLM response as JSON: {e}")
            self.logger.error(f"Raw response: {analysis_result}")

            # If JSON parsing fails, create a basic response
            return {
                "summary": "HTML comparison completed with parsing issues",
                "total_changes": 1,
                "confidence_score": 0.3,
                "added_elements": [],
                "removed_elements": [],
                "modified_elements": [
                    HTMLDifference(
                        type="modified",
                        element="document",
                        description="Unable to parse detailed changes",
                        location="unknown"
                    )
                ]
            }

    def _ensure_deterministic_rag_data(self, rag_data: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure RAG data is deterministically ordered for consistent results."""
        if not rag_data:
            return rag_data
            
        # Sort examples by relevance score (descending), then by document content for tie-breaking
        if 'examples' in rag_data and rag_data['examples']:
            rag_data['examples'] = sorted(
                rag_data['examples'],
                key=lambda x: (-x.get('relevance_score', 0), x.get('document', '')[:100])
            )
            
        # Sort patterns similarly
        if 'patterns' in rag_data and rag_data['patterns']:
            rag_data['patterns'] = sorted(
                rag_data['patterns'],
                key=lambda x: (-x.get('relevance_score', 0), x.get('document', '')[:100])
            )
            
        return rag_data

    def _create_html_search_query(self, html1: str, html2: str) -> str:
        """Create search query for HTML comparison RAG retrieval."""
        # Extract key elements from both HTML documents
        key_elements = []
        
        # Simple extraction of common HTML elements and attributes
        for html in [html1, html2]:
            if 'table' in html.lower():
                key_elements.append('table comparison')
            if 'td' in html.lower():
                key_elements.append('cell changes')
            if 'class=' in html.lower():
                key_elements.append('style modifications')
            if 'id=' in html.lower():
                key_elements.append('element identification')
        
        # Create search query
        base_query = "HTML document comparison changes differences"
        if key_elements:
            base_query += " " + " ".join(sorted(set(key_elements)))  # Sort for determinism
        
        return base_query

    def _build_rag_enhanced_html_prompt(self, rag_data: Dict[str, Any]) -> str:
        """Build RAG-enhanced system prompt for HTML comparison."""
        base_prompt = """You are an expert HTML comparison analyst. Compare HTML documents and identify CONTENT and STRUCTURAL differences only.

IMPORTANT FOCUS AREAS:
- ONLY detect changes in actual content (text, data, values)
- ONLY detect structural changes (new sections, removed sections, element hierarchy)
- IGNORE all CSS/style-related changes (colors, fonts, positioning, styling)
- IGNORE changes in class, id, style attributes (these have been pre-filtered)
- Focus on meaningful business data and content changes

CRITICAL: Your response must be ONLY valid JSON. Do not include any text before or after the JSON. Do not wrap in markdown code blocks.

RESPONSE FORMAT: Return valid JSON with this exact structure:
{
    "summary": "Brief description of content/structural changes found",
    "total_changes": 0,
    "confidence_score": 0.8,
    "added_elements": [
        {
            "type": "added",
            "element": "element_name",
            "location": "xpath_or_selector",
            "description": "Description of new content or section added"
        }
    ],
    "removed_elements": [
        {
            "type": "removed",
            "element": "element_name",
            "location": "xpath_or_selector",
            "description": "Description of content or section removed"
        }
    ],
    "modified_elements": [
        {
            "type": "modified",
            "element": "element_name",
            "location": "xpath_or_selector",
            "old_value": "previous_content_value",
            "new_value": "new_content_value",
            "description": "Description of content that changed"
        }
    ]
}

COUNTING RULES:
- total_changes = len(added_elements) + len(removed_elements) + len(modified_elements)
- Count each distinct CONTENT change only once
- Be precise and accurate
- If no content/structural changes found, return empty arrays and total_changes: 0

CONTENT CHANGE EXAMPLES TO DETECT:
- New table rows with data
- Changed text values or numbers
- Added/removed form fields
- New sections or paragraphs
- Modified headings or labels
- Changed data in cells or fields

STYLE CHANGES TO IGNORE:
- Color changes
- Font changes
- Size/positioning changes
- Class or ID attribute changes
- CSS styling modifications
- Layout or visual formatting changes

IMPORTANT:
- All string values must be properly escaped for JSON
- Use double quotes for all strings
- Ensure valid JSON syntax
- Do not include comments in JSON
- Return ONLY the JSON object, nothing else"""

        # Add RAG examples if available
        if rag_data and rag_data.get('examples'):
            base_prompt += "\n\nKNOWLEDGE BASE EXAMPLES:"
            for i, example in enumerate(rag_data['examples'][:2], 1):
                base_prompt += f"\n\nExample {i}: {example.get('document', '')[:300]}..."

        return base_prompt

    def get_comparison_debug_info(self, comparison_id: str = None) -> Dict[str, Any]:
        """
        Get debugging information for a comparison that had issues.

        Args:
            comparison_id: The comparison ID to get debug info for (optional)

        Returns:
            Debug information dictionary
        """
        return {
            "comparison_id": comparison_id,
            "service_status": {
                "llm_available": self.llm is not None,
                "rag_service_available": self.rag_service is not None,
                "openai_model": getattr(settings, 'OPENAI_MODEL', 'Not configured'),
                "max_tokens": getattr(settings, 'MAX_TOKENS', 'Not configured')
            },
            "common_issues": [
                "LLM response not in valid JSON format",
                "HTML content too large or malformed",
                "Missing required fields in LLM response",
                "Network timeout during LLM call",
                "RAG service retrieval failure"
            ],
            "troubleshooting_steps": [
                "Check application logs for detailed error messages",
                "Verify OpenAI API key and model configuration",
                "Ensure HTML content is well-formed and not too large",
                "Test with smaller HTML samples",
                "Check RAG service connectivity"
            ]
        }
