from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
import os
from dotenv import load_dotenv

from app.api import upload, validation, rag, html_comparison
from app.core.config import settings

# Load environment variables
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="AI-powered XML report validation system with RAG capabilities and vector database integration",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(upload.router, prefix="/api/v1", tags=["upload"])
app.include_router(validation.router, prefix="/api/v1", tags=["validation"])
app.include_router(rag.router, prefix="/api/v1", tags=["rag"])
app.include_router(html_comparison.router, prefix="/api/v1/html", tags=["html-comparison"])

@app.get("/test-html-comparison", include_in_schema=False)
async def get_test_html_page():
    return FileResponse("test_html_comparison.html")

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "XML Report Validation System with AI & RAG",
        "version": settings.APP_VERSION,
        "features": [
            "AI-powered validation",
            "RAG (Retrieval-Augmented Generation)",
            "Vector database integration",
            "Client-specific filtering",
            "Three-status classification",
            "XML output support",
            "HTML comparison and analysis"
        ],
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy", 
        "service": "xml-validation-system",
        "rag_enabled": True,
        "vector_db_accessible": settings.is_vector_db_accessible
    }

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    import traceback
    error_details = f"Internal server error: {str(exc)}"
    traceback_info = traceback.format_exc()
    
    print(f"Global exception handler caught: {error_details}")
    print(f"Traceback: {traceback_info}")
    
    return JSONResponse(
        status_code=500,
        content={"detail": error_details}
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
